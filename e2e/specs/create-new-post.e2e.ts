import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Wait for async operations to complete
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Wait for files to be created in a directory with smart polling
 */
async function waitForFileCreation(
  directory: string,
  expectedCount: number = 1,
  timeout: number = 3000
): Promise<string[]> {
  const startTime = Date.now();
  let lastFileCount = 0;

  while (Date.now() - startTime < timeout) {
    if (fs.existsSync(directory)) {
      const files = fs.readdirSync(directory).filter(file => file.endsWith('.md'));

      // Log progress if file count changes
      if (files.length !== lastFileCount) {
        console.log(`📁 Found ${files.length} files (expecting ${expectedCount})`);
        lastFileCount = files.length;
      }

      if (files.length >= expectedCount) {
        return files;
      }
    }

    // Use faster polling for quicker detection
    await new Promise(resolve => setTimeout(resolve, 25));
  }

  const actualFiles = fs.existsSync(directory)
    ? fs.readdirSync(directory).filter(file => file.endsWith('.md'))
    : [];

  throw new Error(
    `Expected ${expectedCount} files to be created within ${timeout}ms, ` +
    `but found ${actualFiles.length}: [${actualFiles.join(', ')}]`
  );
}

describe("Ghost Sync - Create New Post E2E Test", () => {

  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Set up Ghost API mocking
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://solnic.ghost.io', // Replace with your Ghost URL
      scenario: 'create-new-post',
      record: shouldRecordGhostAPI()
    });

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Plugin should be ready immediately - no wait needed
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clear any existing files in the articles directory
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
  });

  test("should create a new post file with correct content using command palette", async () => {
    const testTitle = "Test Post Title E2E";

    // Open command palette using keyboard shortcut
    await page.keyboard.down('Meta'); // Cmd on Mac
    await page.keyboard.press('KeyP');
    await page.keyboard.up('Meta');

    // Type the command name and wait for it to appear
    await page.keyboard.type('Ghost Sync: Create new post');

    // Press Enter to execute the command
    await page.keyboard.press('Enter');

    // Wait for the "Enter post title" dialog to appear
    await waitForAsyncOperation(2000);

    // Look for the modal and input field
    const modalCheck = await page.evaluate(() => {
      const modals = document.querySelectorAll('.modal, .modal-container, .modal-backdrop');
      const titleInputs = document.querySelectorAll('input[placeholder="Post title..."], input[placeholder*="title"], input[placeholder*="Title"]');
      const textInputs = document.querySelectorAll('input[type="text"]');
      const notices = document.querySelectorAll('.notice');

      return {
        hasModal: modals.length > 0,
        hasTitleInput: titleInputs.length > 0,
        hasTextInput: textInputs.length > 0,
        hasNotice: notices.length > 0,
        modalCount: modals.length,
        titleInputCount: titleInputs.length,
        textInputCount: textInputs.length,
        noticeCount: notices.length
      };
    });

    console.log(`Modal check: ${JSON.stringify(modalCheck)}`);

    // Try to find the title input with various selectors
    let titleInput;
    try {
      if (modalCheck.hasTitleInput) {
        titleInput = page.locator('input[placeholder="Post title..."], input[placeholder*="title"], input[placeholder*="Title"]').first();
      } else if (modalCheck.hasTextInput) {
        titleInput = page.locator('input[type="text"]').first();
      } else {
        // If no input found, check if there's a notice indicating an error
        if (modalCheck.hasNotice) {
          const noticeText = await page.evaluate(() => {
            const notices = document.querySelectorAll('.notice');
            return Array.from(notices).map(n => n.textContent).join(', ');
          });
          console.log(`Notice found instead of modal: ${noticeText}`);
          throw new Error(`Command may have failed. Notice: ${noticeText}`);
        }
        throw new Error('No title input field found and no error notice');
      }

      await titleInput.waitFor({ timeout: 5000 });
      console.log("Found post title input field");
    } catch (error) {
      console.log(`Error finding title input: ${error.message}`);
      throw error;
    }

    // Click on the input field and type the title
    await titleInput.fill(testTitle);

    console.log(`Typed title: ${testTitle}`);

    // Find and click the "Create" button - no explicit wait needed, click() auto-waits
    const createButton = page.locator('button:has-text("Create")');
    await createButton.click();

    console.log("Clicked Create button, waiting for file creation...");

    // Wait for the specific file to be created
    const expectedFileName = `${testTitle.toLowerCase().replace(/\s+/g, '-')}.md`;
    const expectedFilePath = path.join(articlesDir, expectedFileName);

    // Wait for the specific file to exist
    let fileExists = false;
    const startTime = Date.now();
    while (Date.now() - startTime < 5000 && !fileExists) {
      fileExists = fs.existsSync(expectedFilePath);
      if (!fileExists) {
        await waitForAsyncOperation(100);
      }
    }

    expect(fileExists).toBe(true);

    const createdFile = expectedFileName;

    expect(createdFile).toBe(expectedFileName);

    // Verify the file content
    const filePath = path.join(articlesDir, createdFile);
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // Check that the file contains the expected frontmatter
    expect(fileContent).toContain(`Title: "${testTitle}"`);
    expect(fileContent).toContain('Status: "draft"');
    expect(fileContent).toContain('Featured Image: null');
    expect(fileContent).toContain('Newsletter: null');

    // Check that the file is opened in Obsidian
    const activeFile = await page.evaluate(() => {
      return (window as any).app.workspace.getActiveFile()?.name;
    });

    expect(activeFile).toBe(createdFile);

    console.log(`✅ Successfully created and verified post: ${createdFile}`);
    console.log(`📄 File content preview:\n${fileContent.substring(0, 200)}...`);
  });

  test("should create a new post using direct command execution", async () => {
    const testTitle = "Direct Command Test Post";

    // Execute the command directly and then handle the dialog
    await page.evaluate(() => {
      console.log('Executing command: ghost-sync:create-new-post');
      (window as any).app.commands.executeCommandById('ghost-sync:create-new-post');
    });

    console.log("Waiting for dialog after direct command execution...");

    // Wait for the input field
    await waitForAsyncOperation(2000);

    const titleInput = page.locator('input[placeholder="Post title..."], input[type="text"]').first();
    await titleInput.waitFor({ timeout: 5000 });
    console.log("Found input field");

    // Clear any existing text and type the title
    await titleInput.fill(testTitle);

    console.log(`Typed title: ${testTitle}`);

    // Use keyboard to submit instead of clicking the button to avoid modal backdrop issues
    await page.keyboard.press('Enter');

    console.log("Clicked Create button");

    // Wait for the specific file to be created
    const expectedFileName = `${testTitle.toLowerCase().replace(/\s+/g, '-')}.md`;
    const expectedFilePath = path.join(articlesDir, expectedFileName);

    console.log("Expected file name:", expectedFileName);
    console.log("Expected file path:", expectedFilePath);

    // Wait for the specific file to exist
    let fileExists = false;
    const startTime = Date.now();
    while (Date.now() - startTime < 5000 && !fileExists) {
      fileExists = fs.existsSync(expectedFilePath);
      if (!fileExists) {
        await waitForAsyncOperation(100);
      }
    }

    expect(fileExists).toBe(true);

    const createdFile = expectedFileName;

    expect(createdFile).toBe(expectedFileName);

    // Verify the file content
    const filePath = path.join(articlesDir, createdFile);
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // Check that the file contains the expected frontmatter
    expect(fileContent).toContain(`Title: "${testTitle}"`);

    console.log(`✅ Successfully created post via direct command: ${createdFile}`);
  });
});
