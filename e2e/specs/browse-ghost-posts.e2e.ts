import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * E2E Tests for Browse Ghost Posts Functionality
 *
 * These tests verify the post browser functionality:
 * 1. Open post browser via command palette
 * 2. Browse and select posts from Ghost
 * 3. Sync selected posts to local files
 * 4. Handle empty post lists
 * 5. Handle search/filtering in post browser
 */

/**
 * Wait for async operations to complete
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Wait for files to be created in a directory with smart polling
 */
async function waitForFileCreation(
  directory: string,
  expectedCount: number = 1,
  timeout: number = 3000
): Promise<string[]> {
  const startTime = Date.now();
  let lastFileCount = 0;

  while (Date.now() - startTime < timeout) {
    if (fs.existsSync(directory)) {
      const files = fs.readdirSync(directory).filter(file => file.endsWith('.md'));

      if (files.length !== lastFileCount) {
        console.log(`📁 Found ${files.length} files (expecting ${expectedCount})`);
        lastFileCount = files.length;
      }

      if (files.length >= expectedCount) {
        return files;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 50));
  }

  const actualFiles = fs.existsSync(directory)
    ? fs.readdirSync(directory).filter(file => file.endsWith('.md'))
    : [];

  throw new Error(
    `Expected ${expectedCount} files to be created within ${timeout}ms, ` +
    `but found ${actualFiles.length}: [${actualFiles.join(', ')}]`
  );
}

describe("Ghost Sync - Browse Ghost Posts E2E Tests", () => {
  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Set up Ghost API mocking for browse posts scenarios
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://solnic.ghost.io',
      scenario: 'browse-ghost-posts',
      record: shouldRecordGhostAPI()
    });

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    await waitForAsyncOperation(500);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.includes('browse-test') && file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
    await waitForAsyncOperation(200);
  });

  test("should open post browser via command palette", async () => {
    // Open command palette
    await page.keyboard.down('Meta'); // Cmd on Mac
    await page.keyboard.press('KeyP');
    await page.keyboard.up('Meta');

    // Type the browse command
    await page.keyboard.type('Browse and sync posts from Ghost');
    await page.keyboard.press('Enter');

    console.log("Executed browse posts command via command palette");

    // Wait for the post browser modal to appear
    await waitForAsyncOperation(2000);

    // Check if post browser modal is visible
    const modalVisible = await page.evaluate(() => {
      // Look for the specific post browser modal elements
      const modals = document.querySelectorAll('.modal-backdrop, .post-browser-modal, .modal, .suggestion-container, .suggester-container');
      return modals.length > 0;
    });

    expect(modalVisible).toBe(true);

    console.log(`✅ Post browser modal opened successfully`);

    // Close the modal by pressing Escape
    await page.keyboard.press('Escape');
    await waitForAsyncOperation(500);
  });

  test("should browse and select a post from Ghost", async () => {
    // Execute the browse command directly
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:browse-ghost-posts');
    });

    console.log("Executed browse posts command directly");

    // Wait for the post browser to load
    await waitForAsyncOperation(2000);

    // Try to find and interact with the post browser
    const browserResult = await page.evaluate(async () => {
      // Look for post browser specific elements
      const suggestions = document.querySelectorAll('.ghost-post-suggestion, .suggestion-item, .suggester-item, .modal-content');

      if (suggestions.length > 0) {
        // If we have suggestions, try to click the first one
        const firstSuggestion = suggestions[0] as HTMLElement;
        firstSuggestion.click();
        return { foundSuggestions: true, count: suggestions.length };
      }

      // If no suggestions found, check if modal is at least open
      const modals = document.querySelectorAll('.modal-backdrop, .post-browser-modal, .modal, .suggestion-container');
      return { foundSuggestions: false, modalOpen: modals.length > 0 };
    });

    // Either we should find suggestions or at least have the modal open
    expect(browserResult.foundSuggestions || browserResult.modalOpen).toBe(true);

    console.log(`✅ Post browser interaction successful`);
    console.log(`Found suggestions: ${browserResult.foundSuggestions}`);

    if (browserResult.foundSuggestions) {
      console.log(`Suggestion count: ${browserResult.count}`);

      // Wait for potential file creation after selection
      await waitForAsyncOperation(3000);

      // Check if any files were created
      if (fs.existsSync(articlesDir)) {
        const files = fs.readdirSync(articlesDir).filter(file => file.endsWith('.md'));
        console.log(`Files created after post selection: ${files.length}`);
      }
    }

    // Close any open modals
    await page.keyboard.press('Escape');
    await waitForAsyncOperation(500);
  });

  test("should execute browse command via direct command execution", async () => {
    // Test that the command can be executed without errors
    const commandResult = await page.evaluate(() => {
      try {
        (window as any).app.commands.executeCommandById('ghost-sync:browse-ghost-posts');
        return { success: true, error: null };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(commandResult.success).toBe(true);

    console.log(`✅ Browse command executed successfully`);

    // Wait for any UI to appear
    await waitForAsyncOperation(2000);

    // Check if any modal or UI appeared
    const uiResult = await page.evaluate(() => {
      const modals = document.querySelectorAll('.modal-backdrop, .post-browser-modal, .modal, .suggestion-container, .suggester-container');
      const notices = document.querySelectorAll('.notice');

      return {
        hasModal: modals.length > 0,
        hasNotice: notices.length > 0,
        modalCount: modals.length,
        noticeCount: notices.length
      };
    });

    // Either a modal should appear or a notice (in case of no posts/errors)
    expect(uiResult.hasModal || uiResult.hasNotice).toBe(true);

    console.log(`Modal appeared: ${uiResult.hasModal}, Notice appeared: ${uiResult.hasNotice}`);

    // Close any open UI
    await page.keyboard.press('Escape');
    await waitForAsyncOperation(500);
  });

  test("should handle post browser with keyboard navigation", async () => {
    // Execute the browse command
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:browse-ghost-posts');
    });

    await waitForAsyncOperation(2000);

    // Try keyboard navigation in the post browser
    const navigationResult = await page.evaluate(async () => {
      // Check if we have a suggestion container or modal
      const containers = document.querySelectorAll('.post-browser-modal, .suggestion-container, .suggester-container, .modal');

      if (containers.length > 0) {
        // Try to navigate with arrow keys
        const keyEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' });
        containers[0].dispatchEvent(keyEvent);

        return { hasContainer: true, containerType: containers[0].className };
      }

      return { hasContainer: false };
    });

    // Use keyboard navigation
    if (navigationResult.hasContainer) {
      await page.keyboard.press('ArrowDown');
      await waitForAsyncOperation(200);
      await page.keyboard.press('ArrowUp');
      await waitForAsyncOperation(200);
    }

    expect(navigationResult.hasContainer).toBe(true);

    console.log(`✅ Keyboard navigation in post browser works`);
    console.log(`Container type: ${navigationResult.containerType}`);

    // Close the browser
    await page.keyboard.press('Escape');
    await waitForAsyncOperation(500);
  });

  test("should verify post browser command is registered", async () => {
    // Verify the command exists and is properly registered
    const commandCheck = await page.evaluate(() => {
      const commands = (window as any).app.commands.commands;
      const browseCommand = commands['ghost-sync:browse-ghost-posts'];

      return {
        commandExists: !!browseCommand,
        commandName: browseCommand?.name,
        commandId: browseCommand?.id
      };
    });

    expect(commandCheck.commandExists).toBe(true);
    expect(commandCheck.commandId).toBe('ghost-sync:browse-ghost-posts');
    expect(commandCheck.commandName).toBe('Ghost Sync: Browse and sync posts from Ghost');

    console.log(`✅ Browse posts command is properly registered`);
    console.log(`Command: ${commandCheck.commandName} (${commandCheck.commandId})`);
  });
});
